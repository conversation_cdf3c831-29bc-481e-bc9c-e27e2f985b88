<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\VerifCitoyenFamilleResquest;
use App\Http\Requests\VerifCitoyenResquest;
use App\Http\Requests\VerifCivilRequest;
use App\Http\Requests\VerifEleveRequest;
use App\Http\Requests\VerifEtudiantRequest;
use App\Services\VerifService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class VerifApiController extends Controller
{
    protected $service;

    public function __construct(VerifService $service)
    {
        $this->service = $service;
    }

    public function verifEtudiant(VerifEtudiantRequest $request)
    {
        $data = $this->service->verifEtudiant(
            $request->input('id_etud'),
            $request->input('date_naissance')
        );
        if (!$data) {
            return response()->json([
                'error' => 'Item not found'
            ], 404); // Code HTTP 404
        }


        return response()->json($data);
    }
    public function verifCitoyen(VerifCitoyenResquest $request)
    {
        
        $data = $this->service->verifCitoyen(
            $request->input('cin'),
            $request->input('jourNaiss'),
            $request->input('moisNaiss'),
            $request->input('anneeNaiss')
        );
        if (!$data) {
            return response()->json([
                'error' => 'Item not found'
            ], 404);
        }
        return response()->json($data);
    }
    public function verifCitoyenFamille(VerifCitoyenFamilleResquest $request)
    {
        $data = $this->service->verifCitoyenFamille(
            $request->input('idEdu'),
            $request->input('jourNaiss'),
            $request->input('moisNaiss'),
            $request->input('anneeNaiss')
        );
        if (!$data) {
            return response()->json([
                'error' => 'Item not found'
            ], 404);
        }
        return response()->json($data);
    }

    // public function verifEleve(VerifEleveRequest $request)
    // {
    //     $data = $this->service->verifEleve(
    //         $request->input('identifiant'),
    //         $request->input('date_naissance')
    //     );
    //     if (!$data) {
    //         return response()->json([
    //             'error' => 'Item not found'
    //         ], 404); // Code HTTP 404
    //     }

    //     return response()->json($data);
    // }

    // public function verifCivile(VerifCivilRequest $request)
    // {
    //     $data = $this->service->verifCivile(
    //         $request->input('cin')
    //     );

    //     if (!$data) {
    //         return response()->json([
    //             'error' => 'Item not found'
    //         ], 404); // Code HTTP 404
    //     }

    //     return response()->json($data);
    // }

}
