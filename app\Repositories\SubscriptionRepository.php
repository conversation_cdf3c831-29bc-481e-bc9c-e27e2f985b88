<?php

namespace App\Repositories;

use App\Models\Subscription;
use App\Criteria\DateRangeCriteria;
use Prettus\Repository\Eloquent\BaseRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class SubscriptionRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'id_subs_type' => '=',
        'client.identity_number' => 'like',
        'id_payment_method' => '=',
        'start_date' => '>=',
        'end_date' => '<=',
        'stage_date_start' => '>=',
        'stage_date_end' => '<=',
        'id_trip' => '=',
        'id_periodicity' => '=',
        'is_reversed' => '=',
        'is_social_affair' => '=',
        'is_printed' => '=',
        'status' => '=',
        'id_parent' => '=',
        'created_at' => 'between'
    ];

    public function model(): string
    {
        return Subscription::class;
    }

    public function boot()
    {
        $this->pushCriteria(app('Prettus\Repository\Criteria\RequestCriteria'));
        $this->pushCriteria(app(DateRangeCriteria::class));
    }

    public function create(array $attributes)
    {
        try {
            DB::beginTransaction();
            $attributes['ref'] = $this->generateUniqueReference();

            if(isset($attributes['id_parent']) && $attributes['id_parent']) {
                if ($attributes['photo'] instanceof \Illuminate\Http\UploadedFile) {
                    $photo = $attributes['photo'];
                    $extension = $photo->getClientOriginalExtension();
                    if (empty($extension) || $photo->getClientOriginalName() === 'blob') {
                        $mimeType = $photo->getMimeType();
                        if (strpos($mimeType, 'jpeg') !== false) {
                            $extension = 'jpg';
                        } elseif (strpos($mimeType, 'png') !== false) {
                            $extension = 'png';
                        } elseif (strpos($mimeType, 'gif') !== false) {
                            $extension = 'gif';
                        } else {
                            $extension = 'jpg';
                        }
                    }
                    $filename = 'subscription_' . time() . '.' . $extension;
                    try {
                        if (!Storage::exists('public/subscriptions')) {
                            Storage::makeDirectory('public/subscriptions');
                        }
                        $path = $photo->storeAs('public/subscriptions', $filename);
                        if ($path) {
                            $attributes['photo'] = str_replace('public/', '', $path);
                        } else {
                            $content = file_get_contents($photo->getRealPath());
                            $manualPath = 'public/subscriptions/' . $filename;
                            $stored = Storage::put($manualPath, $content);
                            if ($stored) {
                                $attributes['photo'] = 'subscriptions/' . $filename;
                            } else {
                                unset($attributes['photo']);
                            }
                        }
                    } catch (\Exception $e) {
                        unset($attributes['photo']);
                    }
                } elseif (is_string($attributes['photo'])) {
                    if (strpos($attributes['photo'], 'data:image') === 0) {
                        $image = $attributes['photo'];
                        $image = str_replace('data:image/jpeg;base64,', '', $image);
                        $image = str_replace('data:image/png;base64,', '', $image);
                        $image = str_replace(' ', '+', $image);
                        $imageName = 'subscription_' . time() . '.jpg';
                        Storage::put('public/subscriptions/' . $imageName, base64_decode($image));
                        $attributes['photo'] = 'subscriptions/' . $imageName;
                    } elseif (strpos($attributes['photo'], 'http') === 0 || strpos($attributes['photo'], 'subscriptions/') === 0) {
                        $parentSubscription = Subscription::find($attributes['id_parent']);
                        if ($parentSubscription && $parentSubscription->photo) {
                            $oldPhotoPath = 'public/' . $parentSubscription->photo;
                            if (Storage::exists($oldPhotoPath)) {
                                $extension = pathinfo($parentSubscription->photo, PATHINFO_EXTENSION) ?: 'jpg';
                                $newFilename = 'subscription_' . time() . '.' . $extension;
                                $newPath = 'subscriptions/' . $newFilename;

                                if (Storage::copy($oldPhotoPath, 'public/' . $newPath)) {
                                    $attributes['photo'] = $newPath;
                                }
                            }
                        }
                    } else {
                        unset($attributes['photo']);

                        $parentSubscription = Subscription::find($attributes['id_parent']);
                        if ($parentSubscription && $parentSubscription->photo) {
                            $oldPhotoPath = 'public/' . $parentSubscription->photo;
                            if (Storage::exists($oldPhotoPath)) {
                                $extension = pathinfo($parentSubscription->photo, PATHINFO_EXTENSION) ?: 'jpg';
                                $newFilename = 'subscription_' . time() . '.' . $extension;
                                $newPath = 'subscriptions/' . $newFilename;

                                if (Storage::copy($oldPhotoPath, 'public/' . $newPath)) {
                                    $attributes['photo'] = $newPath;
                                }
                            }
                        }
                    }
                } else {
                    $parentSubscription = Subscription::find($attributes['id_parent']);
                    if ($parentSubscription && $parentSubscription->photo) {
                        $oldPhotoPath = 'public/' . $parentSubscription->photo;
                        if (Storage::exists($oldPhotoPath)) {
                            $extension = pathinfo($parentSubscription->photo, PATHINFO_EXTENSION) ?: 'jpg';
                            $newFilename = 'subscription_' . time() . '.' . $extension;
                            $newPath = 'subscriptions/' . $newFilename;

                            if (Storage::copy($oldPhotoPath, 'public/' . $newPath)) {
                                $attributes['photo'] = $newPath;
                            }
                        }
                    }
                }
            }

            elseif (isset($attributes['photo'])) {
                if ($attributes['photo'] instanceof \Illuminate\Http\UploadedFile) {
                    $photo = $attributes['photo'];

                    $extension = $photo->getClientOriginalExtension();
                    if (empty($extension) || $photo->getClientOriginalName() === 'blob') {
                        $mimeType = $photo->getMimeType();
                        if (strpos($mimeType, 'jpeg') !== false) {
                            $extension = 'jpg';
                        } elseif (strpos($mimeType, 'png') !== false) {
                            $extension = 'png';
                        } elseif (strpos($mimeType, 'gif') !== false) {
                            $extension = 'gif';
                        } else {
                            $extension = 'jpg';
                        }
                    }

                    $filename = 'subscription_' . time() . '.' . $extension;

                    try {
                        if (!Storage::exists('public/subscriptions')) {
                            Storage::makeDirectory('public/subscriptions');
                        }

                        $path = $photo->storeAs('public/subscriptions', $filename);

                        if ($path) {
                            $attributes['photo'] = str_replace('public/', '', $path);
                        } else {
                            $content = file_get_contents($photo->getRealPath());
                            $manualPath = 'public/subscriptions/' . $filename;
                            $stored = Storage::put($manualPath, $content);

                            if ($stored) {
                                $attributes['photo'] = 'subscriptions/' . $filename;
                            } else {
                                unset($attributes['photo']);
                            }
                        }
                    } catch (\Exception $e) {
                        unset($attributes['photo']);
                    }
                } elseif (is_string($attributes['photo'])) {
                    if (strpos($attributes['photo'], 'data:image') === 0) {
                        $image = $attributes['photo'];
                        $image = str_replace('data:image/jpeg;base64,', '', $image);
                        $image = str_replace('data:image/png;base64,', '', $image);
                        $image = str_replace(' ', '+', $image);
                        $imageName = 'subscription_' . time() . '.jpg';
                        Storage::put('public/subscriptions/' . $imageName, base64_decode($image));
                        $attributes['photo'] = 'subscriptions/' . $imageName;
                    } elseif (strpos($attributes['photo'], 'http') === 0 || strpos($attributes['photo'], 'subscriptions/') === 0) {
                        // Ne rien faire
                    } else {
                        unset($attributes['photo']);
                    }
                } else {
                    unset($attributes['photo']);
                }
            }

            $subscription = parent::create($attributes);

            DB::commit();
            return $subscription->load(['subsType', 'client', 'paymentMethod', 'trip', 'periodicity', 'parentSubscription']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(array $attributes, $id)
    {
        try {
            DB::beginTransaction();

            $subscription = $this->find($id);
            if (isset($attributes['photo'])) {
                $deleteOldPhoto = function() use ($subscription) {
                    if ($subscription->photo && Storage::exists('public/' . $subscription->photo)) {
                        Storage::delete('public/' . $subscription->photo);
                    }
                };

                if ($attributes['photo'] instanceof \Illuminate\Http\UploadedFile) {
                    $deleteOldPhoto();
                    $photo = $attributes['photo'];
                    $directory = 'public/subscriptions';
                    if (!Storage::exists($directory)) {
                        Storage::makeDirectory($directory);
                    }
                    $extension = $photo->getClientOriginalExtension();
                    if (empty($extension)) {
                        $extension = 'jpg';
                    }
                    $filename = 'subscription_' . time() . '.' . $extension;
                    try {
                        $path = $photo->storeAs($directory, $filename);
                        if ($path === false) {
                            $content = file_get_contents($photo->getRealPath());
                            $path = $directory . '/' . $filename;
                            $stored = Storage::put($path, $content);

                            if (!$stored) {
                                throw new \Exception('Failed to store file manually');
                            }
                        }
                        $attributes['photo'] = str_replace('public/', '', $path);
                    } catch (\Exception $e) {
                        unset($attributes['photo']);
                    }
                } elseif (is_string($attributes['photo'])) {
                    if (strpos($attributes['photo'], 'data:image') === 0) {
                        $deleteOldPhoto();

                        $image = $attributes['photo'];
                        $image = str_replace('data:image/jpeg;base64,', '', $image);
                        $image = str_replace('data:image/png;base64,', '', $image);
                        $image = str_replace(' ', '+', $image);
                        $imageName = 'subscription_' . time() . '.jpg';
                        Storage::put('public/subscriptions/' . $imageName, base64_decode($image));
                        $attributes['photo'] = 'subscriptions/' . $imageName;
                    } elseif (strpos($attributes['photo'], 'http') === 0 || strpos($attributes['photo'], 'subscriptions/') === 0) {
                        if ($attributes['photo'] === $subscription->photo) {
                            unset($attributes['photo']);
                        }
                    } else {
                        try {
                            $deleteOldPhoto();
                            $directory = 'public/subscriptions';
                            if (!Storage::exists($directory)) {
                                Storage::makeDirectory($directory);
                            }
                            $imageName = 'subscription_' . time() . '.jpg';
                            $path = $directory . '/' . $imageName;
                            $stored = Storage::put($path, $attributes['photo']);
                            if (!$stored) {
                                throw new \Exception('Failed to store binary photo data');
                            }
                            $attributes['photo'] = 'subscriptions/' . $imageName;
                        } catch (\Exception $e) {
                            unset($attributes['photo']);
                        }
                    }
                } else {
                    unset($attributes['photo']);
                }
            }
            elseif (isset($attributes['id_parent']) && $attributes['id_parent']) {
                $parentSubscription = Subscription::find($attributes['id_parent']);
                if ($parentSubscription && $parentSubscription->photo) {
                    $oldPhotoPath = 'public/' . $parentSubscription->photo;
                    if (Storage::exists($oldPhotoPath)) {
                        $extension = pathinfo($parentSubscription->photo, PATHINFO_EXTENSION) ?: 'jpg';
                        $newFilename = 'subscription_' . time() . '.' . $extension;
                        $newPath = 'subscriptions/' . $newFilename;

                        if (Storage::copy($oldPhotoPath, 'public/' . $newPath)) {
                            $attributes['photo'] = $newPath;
                        }
                    }
                }
            }

            $subscription = parent::update($attributes, $id);

            DB::commit();
            return $subscription->load(['subsType', 'client', 'paymentMethod', 'trip', 'periodicity', 'parentSubscription']);
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function delete($id)
    {
        try {
            DB::beginTransaction();
            $subscription = $this->find($id);
            if ($subscription->photo && Storage::exists('public/' . $subscription->photo)) {
                Storage::delete('public/' . $subscription->photo);
            }

            $result = parent::delete($id);

            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

 
    protected function generateUniqueReference(): string
    {
        $timestamp = now()->format('YmdHis');
        $random = mt_rand(1000, 9999);
        $reference = "SUB-{$timestamp}-{$random}";
        while ($this->model->where('ref', $reference)->exists()) {
            $random = mt_rand(1000, 9999);
            $reference = "SUB-{$timestamp}-{$random}";
        }
        return $reference;
    }
}


