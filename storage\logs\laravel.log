[2025-06-03 11:18:31] local.INFO: SubscriptionRepository::photo string  {"id_parent":"30","photo":{"Illuminate\\Http\\UploadedFile":"C:\\xampp\\tmp\\phpD35F.tmp"},"att":{"id_subs_type":"3","is_stagiaire":"0","id_periodicity":"1","id_sale_period":"3","is_reversed":false,"photo":{"Illuminate\\Http\\UploadedFile":"C:\\xampp\\tmp\\phpD35F.tmp"},"hasVacances":false,"status":"NOTPAYED","id_parent":"30","renewal_date":"2025-06-03","special_client":"CIVIL","rest_days":["1"],"id_client":90,"id_subscriber":1,"id_trip":466,"ref":"SUB-20250603111831-9610"}} 
[2025-06-03 11:21:24] local.INFO: SubscriptionRepository::create - <PERSON><PERSON> with new uploaded photo {"id_parent":"30","photo_name":"blob","photo_size":33462} 
[2025-06-03 11:21:24] local.INFO: SubscriptionRepository::create - New photo stored successfully {"stored_path":""} 
[2025-06-03 11:23:42] local.INFO: SubscriptionRepository::create - Renewal with new uploaded photo {"id_parent":"30","photo_name":"blob","photo_size":33462,"mime_type":"image/jpeg","extension":""} 
[2025-06-03 11:23:42] local.INFO: SubscriptionRepository::create - New photo stored successfully {"stored_path":"subscriptions/subscription_1748949822.jpg","full_path":"public/subscriptions/subscription_1748949822.jpg"} 
[2025-06-03 11:56:28] local.INFO: SubscriptionRepository::photo string  {"ATT":{"id_subs_type":"3","is_stagiaire":"0","id_periodicity":"1","id_sale_period":"3","is_reversed":false,"photo":{"Illuminate\\Http\\UploadedFile":"C:\\xampp\\tmp\\phpA1BB.tmp"},"hasVacances":false,"status":"NOTPAYED","special_client":"UNIVERSITAIRE","rest_days":["2"],"id_client":95,"id_subscriber":1,"id_trip":466,"ref":"SUB-20250603115628-1370"}} 
[2025-06-09 15:08:35] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'srtgn.subscribers' doesn't exist (Connection: mysql, SQL: select * from `subscribers` where `email` = <EMAIL> limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'srtgn.subscribers' doesn't exist (Connection: mysql, SQL: select * from `subscribers` where `email` = <EMAIL> limit 1) at C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(139): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\tymon\\jwt-auth\\src\\JWTGuard.php(121): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Array)
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(340): Tymon\\JWTAuth\\JWTGuard->attempt(Array)
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\app\\Http\\Controllers\\AuthController.php(43): Illuminate\\Auth\\AuthManager->__call('attempt', Array)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AuthController->login(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('login', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AuthController), 'login')
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\app\\Http\\Middleware\\SetLocale.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(90): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), '5', '1')
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#56 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'srtgn.subscribers' doesn't exist at C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:423)
[stacktrace]
#0 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(423): PDO->prepare('select * from `...')
#1 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(739): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(723): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(333): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(139): Illuminate\\Database\\Eloquent\\Builder->first()
#12 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\tymon\\jwt-auth\\src\\JWTGuard.php(121): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Array)
#13 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(340): Tymon\\JWTAuth\\JWTGuard->attempt(Array)
#14 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\app\\Http\\Controllers\\AuthController.php(43): Illuminate\\Auth\\AuthManager->__call('attempt', Array)
#15 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AuthController->login(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('login', Array)
#17 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AuthController), 'login')
#18 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\app\\Http\\Middleware\\SetLocale.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(90): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#27 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), '5', '1')
#28 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#30 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#31 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#32 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\OneDrive\\Desktop\\SRTGN\\srtgn-backend-public\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#58 {main}
"} 
